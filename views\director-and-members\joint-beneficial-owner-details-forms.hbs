<main class="">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <h4 class="mb-3"><strong>Joint Beneficial Owner Details</strong></h4>
          <!-- Horizontally scrollable vertical table: left labels, one column per person -->
          <div class="table-responsive mb-3" style="overflow-x: auto;">
            <table class="table table-sm table-striped mb-0" style="min-width: 900px;">
              <thead>
                <tr>
                  <th class="header-30-percent">Individual Details</th>
                  {{#each beneficialOwners}}
                  <th>{{BOName}}</th>
                  {{/each}}
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th class="header-30-percent"><strong>Former Name</strong></th>
                  {{#each beneficialOwners}}<td>{{BOFormerName}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent"><strong>Aliases</strong></th>
                  {{#each beneficialOwners}}<td>{{BOAliases}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Date of Birth</strong>
                    {{> director-and-members/required-info-icon field="BODateOfBirth" missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{formatDate BODateOfBirth "YYYY-MM-DD"}}</td>
                  {{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Place of Birth</strong>
                    {{> director-and-members/required-info-icon field="BOBirthCountry" missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BOBirthCountry}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Nationality</strong>
                    {{> director-and-members/required-info-icon field="BONationality" missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BONationality}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Gender</strong>
                    {{> director-and-members/required-info-icon field="BOGender" missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BOGender}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Occupation</strong>
                    {{> director-and-members/required-info-icon field="BOOccupation" missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BOOccupation}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Address</strong>
                    {{> director-and-members/required-info-icon field="BOResidentialAddress"
                    missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BOResidentialAddress}}</td>{{/each}}
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Joint Owner Type</strong>
                    {{> director-and-members/required-info-icon field="BORegistrableCapacityCode"
                    missingValues=../missingValues}}
                  </th>
                  {{#each beneficialOwners}}<td>{{BORegistrableCapacity}}</td>{{/each}}
                </tr>
              </tbody>
            </table>
          </div>

          <h4 class="mb-3"><strong>Beneficial Ownership Interest</strong></h4>
          <div class="table-responsive mb-3" style="overflow-x: auto;">
            <table class="table table-sm table-striped mb-0" style="min-width: 900px;">
              <tbody>
                {{!-- Ownership case: VGNI10 / VGNI11 --}}
                {{#ifEquals (getFirstBONature beneficialOwners) "VGNI10"}}
                <tr>
                  <th class="header-30-percent">
                    <strong>Ownership Percentage</strong>
                    {{> director-and-members/required-info-icon
                    field="Ownership Percentage"
                    missingValues=missingValues}}
                  </th>
                  <td>
                    {{#if beneficialOwners.0.BOInterestPercent}}
                    {{beneficialOwners.0.BOInterestPercent}}%
                    {{/if}}
                  </td>
                </tr>
                <tr>
                  <th class="header-30-percent"><strong>Voting Rights Percentage</strong></th>
                  <td>
                    {{#if beneficialOwners.0.BOVotePercent}}
                    {{#ifEquals beneficialOwners.0.BOVotePercent
                    0}}{{else}}{{beneficialOwners.0.BOVotePercent}}%{{/ifEquals}}
                    {{/if}}
                  </td>
                </tr>

                {{else ifEquals (getFirstBONature beneficialOwners) "VGNI11"}}
                <tr>
                  <th class="header-30-percent">
                    <strong>Ownership Percentage</strong>
                    {{> director-and-members/required-info-icon
                    field="Ownership Percentage"
                    missingValues=missingValues}}
                  </th>
                  <td>
                    {{#if beneficialOwners.0.BOInterestPercent}}
                    {{beneficialOwners.0.BOInterestPercent}}%
                    {{/if}}
                  </td>
                </tr>
                <tr>
                  <th class="header-30-percent"><strong>Voting Rights Percentage</strong></th>
                  <td>
                    {{#if beneficialOwners.0.BOVotePercent}}
                    {{#ifEquals beneficialOwners.0.BOVotePercent
                    0}}{{else}}{{beneficialOwners.0.BOVotePercent}}%{{/ifEquals}}
                    {{/if}}
                  </td>
                </tr>

                {{!-- Control case: VGNI12 --}}
                {{else ifEquals (getFirstBONature beneficialOwners) "VGNI12"}}
                <tr>
                  <th class="header-30-percent"><strong>Voting Rights Percentage</strong></th>
                  <td>
                    {{#if beneficialOwners.0.BOVotePercent}}
                    {{#ifEquals beneficialOwners.0.BOVotePercent
                    0}}{{else}}{{beneficialOwners.0.BOVotePercent}}%{{/ifEquals}}
                    {{/if}}
                  </td>
                </tr>
                <tr>
                  <th class="header-30-percent">
                    <strong>Interest by Control</strong>
                    {{> director-and-members/required-info-icon
                    field="Interest by Control Details"
                    missingValues=missingValues}}
                  </th>
                  <td>
                    {{#if beneficialOwners.0.BOInterestByControl1}}{{beneficialOwners.0.BOInterestByControl1}}{{/if}}
                    {{#if beneficialOwners.0.BOInterestByControl2}}{{#if
                    beneficialOwners.0.BOInterestByControl1}}<br>{{/if}}{{beneficialOwners.0.BOInterestByControl2}}{{/if}}
                    {{#if beneficialOwners.0.BOInterestByControl3}}
                    {{#if beneficialOwners.0.BOInterestByControl1}}<br>{{/if}}
                    {{#unless beneficialOwners.0.BOInterestByControl1}}{{#if
                    beneficialOwners.0.BOInterestByControl2}}<br>{{/if}}{{/unless}}
                    {{beneficialOwners.0.BOInterestByControl3}}
                    {{/if}}
                    {{#if beneficialOwners.0.BOInterestByControlOther}}
                    {{#if beneficialOwners.0.BOInterestByControl1}}<br>{{/if}}
                    {{#unless beneficialOwners.0.BOInterestByControl1}}{{#if
                    beneficialOwners.0.BOInterestByControl2}}<br>{{/if}}{{/unless}}
                    {{#unless beneficialOwners.0.BOInterestByControl1}}{{#unless
                    beneficialOwners.0.BOInterestByControl2}}{{#if
                    beneficialOwners.0.BOInterestByControl3}}<br>{{/if}}{{/unless}}{{/unless}}
                    {{beneficialOwners.0.BOInterestByControlOther}}
                    {{/if}}
                  </td>
                </tr>
                {{/ifEquals}}

                <tr>
                  <th class="header-30-percent">
                    <strong>Effective Date</strong>
                    {{> director-and-members/required-info-icon
                    field="BODateCommenced"
                    missingValues=missingValues}}
                  </th>
                  <td>
                    {{formatDate beneficialOwners.0.BODateCommenced "YYYY-MM-DD"}}
                  </td>
                </tr>
                <tr>
                  <th class="header-30-percent"><strong>Cessation Date</strong></th>
                  <td>
                    {{#if beneficialOwners.0.BODateCeased}}
                    {{formatDate beneficialOwners.0.BODateCeased "YYYY-MM-DD"}}
                    {{/if}}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          {{!-- View History from the first entry if available --}}
          {{#each beneficialOwners}}
          {{#if @first}}
          {{#if showHistory}}
          <button data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
            <small>View History</small>
          </button>
          {{/if}}
          {{/if}}
          {{/each}}

          <!-- Verification Questions -->
          <div class="mb-4 mt-4">
            <table class="table" id="verification-question-table">
              <tbody>
                {{#unless isConfirmed}}
                {{#unless missingValues}}
                <tr id="is-correct-row">
                  <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate
                      and complete?</strong></th>
                  <td>
                    <div class="custom-control custom-radio custom-control-inline">
                      <input type="radio" id="information-correct-yes" name="information-correct"
                        class="custom-control-input" value="yes">
                      <label class="custom-control-label" for="information-correct-yes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                      <input type="radio" id="information-correct-no" name="information-correct"
                        class="custom-control-input" value="no">
                      <label class="custom-control-label" for="information-correct-no">No</label>
                    </div>
                  </td>
                </tr>
                {{/unless}}
                {{/unless}}
              </tbody>
            </table>
          </div>

          <div>
            <a href="/masterclients/{{masterClientCode}}/director-and-members/{{company.code}}/beneficial-owners"
              class="btn btn-secondary waves-effect waves-light width-xl">
              Back
            </a>
            {{#if showConfirmButton}}
            <button id="confirmBtn" class="btn solid royal-blue width-xl ml-2 hide-element"
              data-joint-code="{{jointCode}}">Confirm</button>
            {{/if}}
            <button type="button" id="requestUpdateBtn" class="btn solid royal-blue width-xl ml-2 hide-element"
              data-joint-code="{{jointCode}}" data-missing-values="{{#if missingValues}}true{{else}}false{{/if}}"
              data-is-confirmed="{{#if isConfirmed}}true{{else}}false{{/if}}">Request Update</button>
          </div>
        </div> <!-- end card-box-->
      </div> <!-- end col -->
    </div> <!-- end row -->

  </div> <!-- end container-fluid -->
</main>
<script type="text/javascript" src="/templates/director-and-members/requestupdatelog.precompiled.js"></script>
<script type="text/javascript" src="/templates/director-and-members/requestupdatepopup.precompiled.js"></script>
<script src="/views-js/director-and-members/joint-beneficial-owner-details-forms.js"></script>
